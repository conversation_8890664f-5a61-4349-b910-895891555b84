<?php

namespace App\Traits;

use Illuminate\Support\Facades\Http;

trait ApiAdapterTrait
{
    protected function sankyuGet($url)
    {
        return json_decode(Http::withHeaders([
            'key' => env('SANKYU_API_KEY'),
            'secret' => env('SANKYU_SECRET_KEY'),
        ])->get($url)->body());
    }

    protected function sankyuPost($url, $data)
    {
        return json_decode(Http::withHeaders([
            'key' => env('SANKYU_API_KEY'),
            'secret' => env('SANKYU_SECRET_KEY'),
        ])->timeout(60)->post($url, $data)->body());
    }
}
