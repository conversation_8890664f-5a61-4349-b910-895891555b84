<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Traits\ApiAdapterTrait;
use Illuminate\Support\Facades\Cache;

class HeaderDataController extends Controller
{
    use ApiAdapterTrait;

    public function index()
    {
        $api_urls = config('sankyu_api.top-bar');
        $api_contact = config('sankyu_api.contact-us');
        $api_general = config('sankyu_api.general');
        $api_top_bar = config('sankyu_api.top-bar-no-goldbar');
        $api_gold = config('sankyu_api.goldbar.category-menu');
        $api_feature = config('sankyu_api.feature-setting');
        $api_goldbar = config('sankyu_api.goldbar.category');

        if (env('SET_SESSION') == 1) {
            // Use cached data
            $top_bars = Cache::get('top_bar_data');
            if ($top_bars == null) {
                $top_bars = $this->sankyuGet($api_urls);
                $top_bars = Cache::remember('top_bar_data', now()->addMinutes(30), function () use ($top_bars) {
                    return $top_bars->data ?? [];
                });
            }

            $data_api_contact = Cache::get('contact_data');
            if ($data_api_contact == null) {
                $data_api_contact = $this->sankyuGet($api_contact);
                $workingTime = Cache::remember('contact_data', now()->addMinutes(30), function () use ($data_api_contact) {
                    return $data_api_contact->working_time ?? [];
                });
            }

            $generalSetting = Cache::get('Setting');
            if ($generalSetting == null) {
                $setting = $this->sankyuGet($api_general);
                $generalSetting = Cache::remember('Setting', now()->addMinutes(30), function () use ($setting) {
                    return $setting ?? [];
                });
            }

            $data_api_top_bar = Cache::get('top-bar-no-goldbar');
            if ($data_api_top_bar == null) {
                $data_api_top_bar = $this->sankyuGet($api_top_bar);
                $data_api_top_bar = Cache::remember('top-bar-no-goldbar', now()->addMinutes(30), function () use ($data_api_top_bar) {
                    return $data_api_top_bar ?? [];
                });
            }

            $data_api_goldbar = Cache::get('data_api_golbar');
            if ($data_api_goldbar == null) {
                $data_api_goldbar = $this->sankyuGet($api_gold);
                $data_api_goldbar = Cache::remember('data_api_golbar', now()->addMinutes(30), function () use ($data_api_goldbar) {
                    return $data_api_goldbar ?? [];
                });
            }

            $data_feature = Cache::get('api_feature');
            if ($data_feature == null) {
                $data_feature = $this->sankyuGet($api_feature);
                $data_feature = Cache::remember('data_feature', now()->addMinutes(30), function () use ($data_feature) {
                    return $data_feature ?? [];
                });
            }

            $goldbar = Cache::get('goldbar');
            if ($goldbar == null) {
                $goldbar = $this->sankyuGet($api_goldbar);
                $goldbar = Cache::remember('goldbar', now()->addMinutes(30), function () use ($goldbar) {
                    return $goldbar ?? [];
                });
            }
        } else {
            // Fetch fresh data
            $top_bars = $this->sankyuGet($api_urls);
            $data_api_contact = $this->sankyuGet($api_contact);
            $generalSetting = $this->sankyuGet($api_general);
            $data_api_top_bar = $this->sankyuGet($api_top_bar);
            $data_api_goldbar = $this->sankyuGet($api_gold);
            $data_feature = $this->sankyuGet($api_feature);
            $goldbar = $this->sankyuGet($api_goldbar);
        }

        // Process the data
        $generalSocial = $generalSetting->data->social_medias ?? [];
        $generalSetting = $generalSetting->data->setting ?? [];
        $workingTime = $data_api_contact->working_time ?? [];

        $data_api_feature = $data_feature->data ?? [];
        $topBarDataCategory = $top_bars->category ?? [];
        $topBarDataCustom = $top_bars->custom_page ?? [];
        $featureGoldSaving = $data_api_feature->gold_saving ?? [];
        $top_bar_v2 = $data_api_top_bar->data ?? [];
        $data_feature = $data_feature->data ?? [];

        $data_api_golbar = $data_api_goldbar->data ?? [];
        $categories = $goldbar->data ?? [];

        // Group goldbar data
        $groupedData = [];
        foreach ($data_api_golbar as $item) {
            $kod_supplier = $item->Kod_Supplier;
            if (!isset($groupedData[$kod_supplier])) {
                $groupedData[$kod_supplier] = $item->nama_Supplier;
            }
        }

        return response()->json([
            'topBarDataCategory' => $topBarDataCategory,
            'topBarDataCustom' => $topBarDataCustom,
            'generalSetting' => $generalSetting,
            'generalSocial' => $generalSocial,
            'featureGoldSaving' => $featureGoldSaving,
            'workingTime' => $workingTime,
            'topBarV2' => $top_bar_v2,
            'goldBar' => $groupedData,
            'features' => $data_feature,
            'topBars' => $top_bars->data ?? $top_bars,
            'goldCategories' => $categories,
            'categories' => $topBarDataCategory,
            'customPages' => $topBarDataCustom,
        ]);
    }
}
