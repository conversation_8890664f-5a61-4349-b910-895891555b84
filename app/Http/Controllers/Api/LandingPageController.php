<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;

class LandingPageController extends Controller
{
    protected function sankyuGet($url)
    {
        return json_decode(Http::withHeaders([
            'key' => env('SANKYU_API_KEY'),
            'secret' => env('SANKYU_SECRET_KEY'),
        ])->get($url)->body());
    }

    protected function sankyuPost($url, $data)
    {
        return json_decode(Http::withHeaders([
            'key' => env('SANKYU_API_KEY'),
            'secret' => env('SANKYU_SECRET_KEY'),
        ])->timeout(60)->post($url, $data)->body());
    }

    public function index()
    {
        $user_category = session('user_category') ?? 1;

        $payload = ['user_category_id' => $user_category];

        $featured = $this->sankyuPost(config('sankyu_api.product.new-featured'), $payload)->data ?? [];
        $goldprices = $this->sankyuGet(config('sankyu_api.gold-price.normal'))->data->gold_price ?? [];
        $goldbarprices = $this->sankyuGet(config('sankyu_api.gold-bar-price.normal'))->data->goldbar ?? [];

        $landing = $this->sankyuGet(config('sankyu_api.landing-page'))->data ?? [];

        $sliders = collect($landing->slider ?? []);
        $desktopSliders = $sliders->where('position', '!=', 3)->where('category', 'desktop')->values();
        $mobileSliders = $sliders->where('position', '!=', 3)->where('category', 'mobile')->values();

        return response()->json([
            'featured' => $featured,
            'goldprices' => $goldprices,
            'goldbarprices' => $goldbarprices,
            'cards' => $landing->card ?? [],
            'brands' => $landing->brand ?? [],
            'categories' => $landing->category ?? [],
            'points' => $landing->point ?? [],
            'videos' => $landing->video ?? [],
            'testimonials' => $landing->testimonial ?? [],
            'sliders' => $desktopSliders,
            'mobilesliders' => $mobileSliders,
        ]);
    }

    public function getHeaderData()
    {
        $user_category = session('user_category') ?? 1;
        $payload = ['user_category_id' => $user_category];
        
        // Get navigation data
        $categories = $this->sankyuGet(config('sankyu_api.categories'))->data ?? [];
        $goldCategories = $this->sankyuGet(config('sankyu_api.gold_categories'))->data ?? [];
        $customPages = $this->sankyuGet(config('sankyu_api.custom_pages'))->data ?? [];
        $generalSettings = $this->sankyuGet(config('sankyu_api.general_settings'))->data ?? [];
        $features = $this->sankyuGet(config('sankyu_api.features'))->data ?? [];
        
        return response()->json([
            'categories' => $categories,
            'goldCategories' => $goldCategories,
            'customPages' => $customPages,
            'generalSettings' => $generalSettings,
            'features' => $features,
            'isLoggedIn' => session()->has('user_id')
        ]);
    }
}
