<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class LandingPageController extends Controller
{
    protected function sankyuGet($url)
    {
        try {
            $apiKey = env('SANKYU_API_KEY');
            $secretKey = env('SANKYU_SECRET_KEY');

            $response = Http::withHeaders([
                'key' => $apiKey,
                'secret' => $secretKey,
            ])->get($url);

            return json_decode($response->body());
        } catch (\Exception $e) {
            Log::error('API Call Failed:', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    protected function sankyuPost($url, $data)
    {
        return json_decode(Http::withHeaders([
            'key' => env('SANKYU_API_KEY'),
            'secret' => env('SANKYU_SECRET_KEY'),
        ])->timeout(60)->post($url, $data)->body());
    }

    public function index()
    {
        $user_category = session('user_category') ?? 1;

        $payload = ['user_category_id' => $user_category];

        $featured = $this->sankyuPost(config('sankyu_api.product.new-featured'), $payload)->data ?? [];
        $goldprices = $this->sankyuGet(config('sankyu_api.gold-price.normal'))->data->gold_price ?? [];
        $goldbarprices = $this->sankyuGet(config('sankyu_api.gold-bar-price.normal'))->data->goldbar ?? [];

        $landingResponse = $this->sankyuGet(config('sankyu_api.landing-page'));



        $landing = $landingResponse->data ?? [];

        $sliders = collect($landing->slider ?? []);
        // For now, return all sliders since the API doesn't have desktop/mobile categories
        $allSliders = $sliders->values();

        return response()->json([
            'featured' => $featured,
            'goldprices' => $goldprices,
            'goldbarprices' => $goldbarprices,
            'cards' => $landing->card ?? [],
            'brands' => $landing->brand ?? [],
            'categories' => $landing->category ?? [],
            'points' => $landing->point ?? [],
            'videos' => $landing->video ?? [],
            'testimonials' => $landing->testimonial ?? [],
            'sliders' => $allSliders,
            'mobilesliders' => $allSliders,
        ]);
    }

}
