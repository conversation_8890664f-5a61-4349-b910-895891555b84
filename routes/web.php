<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Landing page route
Route::get('/', function () {
    return Inertia::render('Landing');
})->name('landing');

Route::get('/landing', function () {
    return Inertia::render('Landing');
})->name('landing.page');

// Include authentication routes
require __DIR__.'/auth.php';

// Catch-all route for other pages (should be last)
Route::get('/{any}', function () {
    return view('app');
})->where('any', '.*');
