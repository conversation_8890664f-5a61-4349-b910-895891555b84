<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\LandingPageController;

Route::get('/landing', [LandingPageController::class, 'index']);

// Header data endpoint
Route::get('/header-data', [LandingPageController::class, 'getHeaderData']);

// Cart and wishlist count endpoints
Route::get('/cart/count', function () {
    return response()->json(['count' => session('cart_count', 0)]);
});

Route::get('/wishlist/count', function () {
    return response()->json(['count' => session('wishlist_count', 0)]);
});
