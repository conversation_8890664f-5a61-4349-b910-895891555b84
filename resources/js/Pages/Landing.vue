<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Hero Section -->
    <div class="container mx-auto px-6 py-12">
      <div class="text-center mb-12">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
          Welcome to RMS GCOMM
        </h1>
        <p class="text-xl text-gray-600 mb-8">
          Your trusted partner for gold trading and investment
        </p>
        <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition duration-300">
          Get Started
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading data...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
        <p><strong>Error:</strong> {{ error }}</p>
        <p class="text-sm mt-1">Please check your API configuration or try again later.</p>
      </div>

      <!-- Main Slider Section -->
      <div v-if="data.sliders && data.sliders.length > 0" class="mb-12">
        <div class="relative overflow-hidden rounded-lg shadow-lg">
          <div class="flex transition-transform duration-500 ease-in-out">
            <div v-for="slide in data.sliders.slice(0, 1)" :key="slide.id" class="w-full flex-shrink-0">
              <img :src="slide.image" :alt="slide.title || 'Slider image'" class="w-full h-64 md:h-96 object-cover" />
              <div v-if="slide.button_name && slide.link" class="absolute bottom-4 left-4">
                <a :href="slide.link" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg transition duration-300">
                  {{ slide.button_name }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cards Section -->
      <div v-if="data.cards && data.cards.length > 0" class="mb-12">
        <h2 class="text-3xl font-semibold text-gray-800 mb-6 text-center">Special Offers</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="card in data.cards" :key="card.id" class="bg-white rounded-lg shadow-lg overflow-hidden">
            <img :src="card.image" :alt="card.title" class="w-full h-48 object-cover" />
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ card.title }}</h3>
              <p class="text-gray-600 mb-4">{{ card.description }}</p>
              <a v-if="card.link" :href="card.link" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-300">
                {{ card.button_name || 'Learn More' }}
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Categories Section -->
      <div v-if="data.categories && data.categories.length > 0" class="mb-12">
        <h2 class="text-3xl font-semibold text-gray-800 mb-6 text-center">Product Categories</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          <div v-for="category in data.categories" :key="category.id" class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
            <img :src="category.img_path" :alt="category.Kategori_Produk" class="w-full h-32 object-cover" />
            <div class="p-4 text-center">
              <h3 class="font-semibold text-gray-800">{{ category.Kategori_Produk }}</h3>
              <p class="text-sm text-gray-600">{{ category.Kod_Kategori_Produk }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Points/Features Section -->
      <div v-if="data.points && data.points.length > 0" class="mb-12">
        <h2 class="text-3xl font-semibold text-gray-800 mb-6 text-center">Our Services</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div v-for="point in data.points" :key="point.id" class="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition duration-300">
            <img :src="point.image" :alt="point.title" class="w-16 h-16 mx-auto mb-4 object-contain" />
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ point.title }}</h3>
            <a v-if="point.description" :href="point.description" class="text-blue-600 hover:text-blue-800 text-sm">Learn More</a>
          </div>
        </div>
      </div>

      <!-- Video Section -->
      <div v-if="data.videos" class="mb-12">
        <h2 class="text-3xl font-semibold text-gray-800 mb-6 text-center">{{ data.videos.title || 'Featured Video' }}</h2>
        <div class="max-w-4xl mx-auto">
          <div class="relative pb-9/16 h-0 overflow-hidden rounded-lg shadow-lg">
            <iframe
              :src="data.videos.video_url"
              class="absolute top-0 left-0 w-full h-96"
              frameborder="0"
              allowfullscreen>
            </iframe>
          </div>
        </div>
      </div>

      <!-- Brands Section -->
      <div v-if="data.brands && data.brands.length > 0" class="mb-12">
        <h2 class="text-3xl font-semibold text-gray-800 mb-6 text-center">Our Brands</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div v-for="brand in data.brands" :key="brand.id" class="bg-white rounded-lg shadow-lg overflow-hidden">
            <img :src="brand.img" :alt="brand.title" class="w-full h-48 object-cover" />
            <div class="p-4">
              <h3 class="text-lg font-semibold text-gray-800">{{ brand.title }}</h3>
            </div>
          </div>
        </div>
      </div>

      <!-- Testimonials Section -->
      <div v-if="data.testimonials && data.testimonials.length > 0" class="mb-12">
        <h2 class="text-3xl font-semibold text-gray-800 mb-6 text-center">Customer Testimonials</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="testimonial in data.testimonials" :key="testimonial.id" class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center mb-4">
              <img :src="testimonial.profile_image" :alt="testimonial.name" class="w-12 h-12 rounded-full object-cover mr-4" />
              <div>
                <h3 class="font-semibold text-gray-800">{{ testimonial.name }}</h3>
                <p class="text-sm text-gray-600">{{ testimonial.caption }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Sliders Gallery -->
      <div v-if="data.sliders && data.sliders.length > 1" class="mt-12">
        <h2 class="text-3xl font-semibold text-gray-800 mb-6 text-center">More Products</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="slide in data.sliders.slice(1)" :key="slide.id" class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
            <img :src="slide.image" :alt="slide.title || 'Product image'" class="w-full h-48 object-cover" />
            <div class="p-4">
              <div v-if="slide.title || slide.description" class="mb-3">
                <h3 v-if="slide.title" class="font-semibold text-gray-800">{{ slide.title }}</h3>
                <p v-if="slide.description" class="text-gray-600 text-sm mt-1">{{ slide.description }}</p>
              </div>
              <a v-if="slide.link" :href="slide.link" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-300 text-sm">
                {{ slide.button_name || 'Shop Now' }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const data = ref({
  featured: [],
  goldprices: [],
  goldbarprices: [],
  cards: [],
  brands: [],
  categories: [],
  points: [],
  videos: null,
  testimonials: [],
  sliders: [],
  mobilesliders: [],
})

const loading = ref(true)
const error = ref(null)

onMounted(async () => {
  try {
    const response = await fetch('/api/landing')

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('API Response:', result) // Debug log
    data.value = result
  } catch (err) {
    console.error('Failed to fetch landing data:', err)
    error.value = err.message

    // Set some demo data for development
    data.value = {
      featured: [
        { id: 1, name: 'Gold Bar 1oz', description: 'Premium 24k gold bar' },
        { id: 2, name: 'Gold Coin Set', description: 'Collectible gold coins' },
        { id: 3, name: 'Investment Package', description: 'Starter investment option' }
      ],
      goldprices: [
        { type: '24K Gold', price: '280.50' },
        { type: '22K Gold', price: '256.80' },
        { type: '18K Gold', price: '210.40' }
      ],
      cards: [],
      brands: [],
      categories: [],
      points: [],
      videos: null,
      testimonials: [],
      sliders: [],
      mobilesliders: []
    }
  } finally {
    loading.value = false
  }
})
</script>
