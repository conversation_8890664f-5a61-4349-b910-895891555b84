<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Hero Section -->
    <div class="container mx-auto px-6 py-12">
      <div class="text-center mb-12">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
          Welcome to RMS GCOMM
        </h1>
        <p class="text-xl text-gray-600 mb-8">
          Your trusted partner for gold trading and investment
        </p>
        <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition duration-300">
          Get Started
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading data...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
        <p><strong>Error:</strong> {{ error }}</p>
        <p class="text-sm mt-1">Please check your API configuration or try again later.</p>
      </div>

      <!-- Content Sections -->
      <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Featured Products -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
            <svg class="w-6 h-6 mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            Featured Products
          </h2>
          <div v-if="data.featured && data.featured.length > 0">
            <div v-for="item in data.featured" :key="item.id" class="border-b border-gray-200 py-2 last:border-b-0">
              <h3 class="font-medium text-gray-800">{{ item.name }}</h3>
              <p class="text-sm text-gray-600">{{ item.description || 'Premium gold product' }}</p>
            </div>
          </div>
          <div v-else class="text-gray-500 text-center py-4">
            <p>No featured products available</p>
          </div>
        </div>

        <!-- Gold Prices -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
            <svg class="w-6 h-6 mr-2 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.736 6.979C9.208 6.193 9.696 6 10 6c.304 0 .792.193 1.264.979.446.743.736 1.979.736 3.021 0 1.042-.29 2.278-.736 3.021C10.792 13.807 10.304 14 10 14c-.304 0-.792-.193-1.264-.979C8.29 12.278 8 11.042 8 10c0-1.042.29-2.278.736-3.021z" clip-rule="evenodd"/>
            </svg>
            Gold Prices
          </h2>
          <div v-if="data.goldprices && data.goldprices.length > 0">
            <div v-for="price in data.goldprices" :key="price.type" class="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
              <span class="font-medium text-gray-700">{{ price.type }}</span>
              <span class="text-lg font-bold text-green-600">RM {{ price.price }}</span>
            </div>
          </div>
          <div v-else class="text-gray-500 text-center py-4">
            <p>Gold prices will be displayed here</p>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
            <svg class="w-6 h-6 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
            </svg>
            Quick Stats
          </h2>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-600">Total Products</span>
              <span class="font-bold">{{ data.featured?.length || 0 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Price Updates</span>
              <span class="font-bold">{{ data.goldprices?.length || 0 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Status</span>
              <span class="font-bold text-green-600">{{ error ? 'Offline' : 'Online' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Image Gallery -->
      <div v-if="data.sliders && data.sliders.length > 0" class="mt-12">
        <h2 class="text-3xl font-semibold text-gray-800 mb-6 text-center">Gallery</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="slide in data.sliders" :key="slide.id" class="bg-white rounded-lg shadow-lg overflow-hidden">
            <img :src="slide.image_url" :alt="slide.title || 'Gallery image'" class="w-full h-48 object-cover" />
            <div v-if="slide.title || slide.description" class="p-4">
              <h3 v-if="slide.title" class="font-semibold text-gray-800">{{ slide.title }}</h3>
              <p v-if="slide.description" class="text-gray-600 text-sm mt-1">{{ slide.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const data = ref({
  featured: [],
  goldprices: [],
  sliders: [],
})

const loading = ref(true)
const error = ref(null)

onMounted(async () => {
  try {
    const response = await fetch('/api/landing')

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    data.value = result
  } catch (err) {
    console.error('Failed to fetch landing data:', err)
    error.value = err.message

    // Set some demo data for development
    data.value = {
      featured: [
        { id: 1, name: 'Gold Bar 1oz', description: 'Premium 24k gold bar' },
        { id: 2, name: 'Gold Coin Set', description: 'Collectible gold coins' },
        { id: 3, name: 'Investment Package', description: 'Starter investment option' }
      ],
      goldprices: [
        { type: '24K Gold', price: '280.50' },
        { type: '22K Gold', price: '256.80' },
        { type: '18K Gold', price: '210.40' }
      ],
      sliders: []
    }
  } finally {
    loading.value = false
  }
})
</script>
