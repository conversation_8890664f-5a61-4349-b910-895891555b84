<template>
  <div class="p-6">
    <h1 class="text-xl font-bold mb-4">Landing Page</h1>

    <h2 class="text-lg font-semibold">Featured Products</h2>
    <ul>
      <li v-for="item in data.featured" :key="item.id">{{ item.name }}</li>
    </ul>

    <h2 class="text-lg font-semibold mt-6">Gold Prices</h2>
    <ul>
      <li v-for="price in data.goldprices" :key="price.type">
        {{ price.type }}: RM {{ price.price }}
      </li>
    </ul>

    <h2 class="text-lg font-semibold mt-6">Sliders</h2>
    <div class="grid grid-cols-2 gap-4">
      <img v-for="slide in data.sliders" :src="slide.image_url" :key="slide.id" class="w-full rounded" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const data = ref({
  featured: [],
  goldprices: [],
  sliders: [],
})

onMounted(async () => {
  const res = await fetch('/api/landing')
  data.value = await res.json()
})
</script>
