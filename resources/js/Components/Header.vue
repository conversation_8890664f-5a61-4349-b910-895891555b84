<template>
  <header class="header-main_area">
    <!-- Header styles -->
    <style>
      .offcanvas-menu-inner {
        max-height: 100%;
        overflow-y: auto;
      }
      .dropdown-menu {
        min-width: 10rem;
        padding: 0.5rem 0;
      }
      .custom-dropdown {
        background: white;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      }
      .hover-black:hover {
        color: #000 !important;
      }
    </style>

    <!-- Main Navigation Bar -->
    <div class="header-bottom_area header-sticky stick" style="background-color: #ffffff">
      <div class="container">
        <div class="row">
          <!-- Logo -->
          <div class="col-md-2 col-sm-2">
            <div class="header-logo" style="padding: 2px">
              <a href="/">
                <img :src="headerData.generalSettings?.ecomm_logo" alt="Logo"> 
              </a>
            </div>
          </div>
          
          <!-- Desktop Navigation -->
          <div class="col-lg-7 d-none d-lg-flex justify-content-center position-static">
            <div class="main-menu_area">
              <nav>
                <ul>
                  <li><a class="text-1" href="/" style="color: #000000">Home</a></li>
                  
                  <!-- Preloved Category -->
                  <li>
                    <a class="text-1" href="/category/preloved" style="color: #000000; position: relative; transform: translateX(-54%); left: 45%;">Preloved</a>
                    <ul class="hm-dropdown text-center custom-dropdown" style="position: absolute; transform: translateX(-54%) scaleY(1); left: 38%;">
                      <li v-for="category in headerData.categories" :key="category.id">
                        <a class="text-1 hover-black" :href="`/category/${category.id}`">{{ category.Kategori_Produk }}</a>
                      </li>
                    </ul>
                  </li>
                  
                  <!-- Gold Wafer -->
                  <li>
                    <a class="text-1" href="/product/goldbar" style="color: #000000; position: relative; transform: translateX(-54%); left: 45%;">Gold Wafer</a>
                    <ul class="hm-dropdown text-center custom-dropdown" style="position: absolute; transform: translateX(-54%) scaleY(1); left: 51%;">
                      <li v-for="goldbar in headerData.goldCategories" :key="goldbar.id">
                        <a class="text-1 hover-black" :href="`/goldbar/design/${goldbar.id}`">{{ goldbar.Kategori_Produk }}</a>
                      </li>
                    </ul>
                  </li>
                  
                  <!-- Gold Price -->
                  <li><a class="text-1" href="/gold-price" style="color: #000000">Gold Price</a></li>
                  
                  <!-- Info Dropdown -->
                  <li>
                    <a class="text-1" href="#" style="color: #000000; position: relative; transform: translateX(-54%); left: 45%;">Info</a>
                    <ul class="hm-dropdown text-center custom-dropdown" style="position: absolute; transform: translateX(-54%) scaleY(1); left: 71.5%;">
                      <li><a class="text-1 hover-black" href="/about-us">About Us</a></li>
                      <li><a class="text-1 hover-black" href="/contact-us">Contact Us</a></li>
                      <li v-if="headerData.features?.combo == 1"><a class="text-1 hover-black" href="/combo">Combo</a></li>
                      <li v-if="headerData.features?.merchandise == 1"><a class="text-1 hover-black" href="/merchandise">Merchandise</a></li>
                      <li v-for="page in headerData.customPages" :key="page.id">
                        <a class="text-1 hover-black" :href="`/page/${page.slug}`">{{ page.name }}</a>
                      </li>
                    </ul>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
          
          <!-- Right Icons -->
          <div class="col-md-3 col-sm-3">
            <div class="header-right_area">
              <ul>
                <!-- Search Icon -->
                <li>
                  <a href="#" @click.prevent="toggleSearch" class="search-btn toolbar-btn">
                    <i class="ion-ios-search-strong" style="color: #000000"></i>
                  </a>
                </li>
                
                <!-- User Account -->
                <li v-if="headerData.isLoggedIn">
                  <a href="#" @click.prevent="toggleAccountMenu" class="minicart-btn toolbar-btn">
                    <i class="ion-person" style="color: #000000"></i>
                  </a>
                </li>
                
                <!-- Cart -->
                <li v-if="headerData.isLoggedIn">
                  <a href="#" @click.prevent="toggleCart" class="minicart-btn toolbar-btn">
                    <i class="ion-bag" style="color: #000000"></i>
                    <span class="cart-item_count" v-if="cartCount > 0">{{ cartCount }}</span>
                  </a>
                </li>
                
                <!-- Wishlist -->
                <li v-if="headerData.isLoggedIn">
                  <a href="/wishlist">
                    <i class="ion-android-favorite-outline" style="color: #000000"></i>
                    <span class="wishlist-item_count" v-if="wishlistCount > 0">{{ wishlistCount }}</span>
                  </a>
                </li>
                
                <!-- Login/Register Dropdown -->
                <li v-else class="dropdown">
                  <a href="#" class="toolbar-btn dropdown-toggle" @click.prevent="toggleAuthDropdown">
                    <i class="ion-person" style="color: #000000;padding-left:30%"></i>
                  </a>
                  <ul class="dropdown-menu" v-show="showAuthDropdown">
                    <li>
                      <a class="dropdown-item" href="/login">
                        <i class="ion-log-in" style="color: #000000"></i> Login
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="/register">
                        <i class="ion-person-add" style="color: #000000"></i> Register
                      </a>
                    </li>
                  </ul>
                </li>
                
                <!-- Mobile Menu Toggle -->
                <li>
                  <a href="#" @click.prevent="toggleMobileMenu" class="mobile-menu_btn toolbar-btn color--white d-lg-none d-block">
                    <i class="ion-navicon" style="color: #000000"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div class="mobile-menu_wrapper" :class="{ active: showMobileMenu }" id="mobileMenu">
      <div class="offcanvas-menu-inner">
        <div class="container">
          <a href="#" class="btn-close" @click.prevent="toggleMobileMenu" style="color:gray;padding-left:20%">x</a>
          <nav class="offcanvas-navigation">
            <ul class="mobile-menu">
              <!-- Mobile menu items -->
              <!-- Similar structure to desktop but adapted for mobile -->
            </ul>
          </nav>
        </div>
      </div>
    </div>
    
    <!-- Mini Cart -->
    <div class="offcanvas-minicart_wrapper" :class="{ active: showCart }" id="miniCart">
      <div class="offcanvas-menu-inner">
        <a href="#" class="btn-close" @click.prevent="toggleCart"><i class="ion-android-close"></i></a>
        <!-- Cart content will be loaded here -->
      </div>
    </div>
    
    <!-- Account Menu -->
    <div class="offcanvas-minicart_wrapper" :class="{ active: showAccountMenu }" id="menuMyAccount">
      <div class="offcanvas-menu-inner">
        <a href="#" class="btn-close" @click.prevent="toggleAccountMenu"><i class="ion-android-close"></i></a>
        <div class="minicart-btn_area">
          <a href="/my-account"><button class="hiraola-register_btn hiraola_btn text-1" style="text-align: center; height: 40px;">My Account</button></a>
        </div>
        <div class="minicart-btn_area">
          <a href="/address"><button class="hiraola-register_btn hiraola_btn text-1" style="text-align: center; height: 40px;">Address</button></a>
        </div>
        <div class="minicart-btn_area">
          <a href="/membership"><button class="hiraola-register_btn hiraola_btn text-1" style="text-align: center; height: 40px;">Upgrade Membership</button></a>
        </div>
        <div class="minicart-btn_area">
          <a href="/order-history"><button class="hiraola-register_btn hiraola_btn text-1" style="text-align: center; height: 40px;">Order History</button></a>
        </div>
        <div class="minicart-btn_area">
          <a href="/change-password"><button class="hiraola-register_btn hiraola_btn text-1" style="text-align: center; height: 40px;">Change Password</button></a>
        </div>
        <div class="minicart-btn_area">
          <a href="/logout"><button class="hiraola-register_btn hiraola_btn text-1" style="text-align: center; height: 40px;">Logout</button></a>
        </div>
      </div>
    </div>
    
    <!-- Search Modal -->
    <div class="search-modal" :class="{ active: showSearch }">
      <div class="search-modal-inner">
        <a href="#" class="btn-close" @click.prevent="toggleSearch"><i class="ion-android-close"></i></a>
        <div class="search-form">
          <input type="text" v-model="searchQuery" placeholder="Search products..." @keyup.enter="performSearch">
          <button @click="performSearch"><i class="ion-ios-search-strong"></i></button>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';

// State variables
const headerData = ref({
  categories: [],
  goldCategories: [],
  customPages: [],
  generalSettings: {},
  features: {},
  isLoggedIn: false
});

const cartCount = ref(0);
const wishlistCount = ref(0);
const searchQuery = ref('');
const showMobileMenu = ref(false);
const showCart = ref(false);
const showAccountMenu = ref(false);
const showSearch = ref(false);
const showAuthDropdown = ref(false);

// Toggle functions
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value;
};

const toggleCart = () => {
  showCart.value = !showCart.value;
};

const toggleAccountMenu = () => {
  showAccountMenu.value = !showAccountMenu.value;
};

const toggleSearch = () => {
  showSearch.value = !showSearch.value;
};

const toggleAuthDropdown = () => {
  showAuthDropdown.value = !showAuthDropdown.value;
};

// Search function
const performSearch = () => {
  if (searchQuery.value.trim()) {
    window.location.href = `/search?q=${encodeURIComponent(searchQuery.value.trim())}`;
  }
};

// Fetch header data
const fetchHeaderData = async () => {
  try {
    const response = await axios.get('/api/header-data');
    headerData.value = response.data;
    
    // If user is logged in, fetch cart and wishlist counts
    if (headerData.value.isLoggedIn) {
      fetchCartCount();
      fetchWishlistCount();
    }
  } catch (error) {
    console.error('Failed to fetch header data:', error);
  }
};

// Fetch cart count
const fetchCartCount = async () => {
  try {
    const response = await axios.get('/api/cart/count');
    cartCount.value = response.data.count;
  } catch (error) {
    console.error('Failed to fetch cart count:', error);
  }
};

// Fetch wishlist count
const fetchWishlistCount = async () => {
  try {
    const response = await axios.get('/api/wishlist/count');
    wishlistCount.value = response.data.count;
  } catch (error) {
    console.error('Failed to fetch wishlist count:', error);
  }
};

// Initialize component
onMounted(() => {
  fetchHeaderData();
  
  // Close dropdowns when clicking outside
  document.addEventListener('click', (event) => {
    if (!event.target.closest('.dropdown')) {
      showAuthDropdown.value = false;
    }
  });
});
</script>

<style scoped>
.header-sticky {
  position: sticky;
  top: 0;
  z-index: 1000;
}

.active {
  display: block !important;
}

.search-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.8);
  z-index: 1100;
}

.search-modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-modal-inner {
  width: 80%;
  max-width: 600px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
}

.search-form {
  display: flex;
}

.search-form input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-right: none;
}

.search-form button {
  padding: 10px 15px;
  background-color: #000;
  color: white;
  border: none;
  cursor: pointer;
}

.cart-item_count, .wishlist-item_count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff2d20;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>