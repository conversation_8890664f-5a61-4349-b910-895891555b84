<template>
  <div>
    <!-- Header -->
    <Header />
    
    <!-- Main Content -->
    <main>
      <slot />
    </main>
    
    <!-- Footer (if needed) -->
    <footer v-if="showFooter" class="footer-area">
      <!-- Footer content can be added here -->
    </footer>
  </div>
</template>

<script setup>
import Header from '@/Components/Header.vue'

// Props
defineProps({
  showFooter: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped>
main {
  min-height: calc(100vh - 80px); /* Adjust based on header height */
}
</style>
